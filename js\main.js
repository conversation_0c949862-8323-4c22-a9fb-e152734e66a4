/**
 * 主程序入口
 */
import BalanceChart from "./components/BalanceChart.js";
import CPSChart from "./components/CPSChart.js";
import InfoDisplay from "./components/InfoDisplay.js";
import SectionTable from "./components/SectionTable.js";
import SolarChart from "./components/SolarChart.js";
import WindChart from "./components/WindChart.js";
import * as data from "./data.js";

// 页面加载完成后初始化所有图表
document.addEventListener("DOMContentLoaded", () => {
  // 初始化平衡曲线图表
  // const balanceChart = new BalanceChart("balance-chart", data.balanceData);

  // 初始化风电消纳曲线图表
  // const windChart = new WindChart("wind-chart", data.windData);

  // 初始化光伏消纳曲线图表
  // const solarChart = new SolarChart("solar-chart", data.solarData);

  // 初始化断面监视表格
  const sectionTable = new SectionTable("section-table");

  // 初始化CPS曲线图表（不传入静态数据，让组件自动从API获取）
  const cpsChart = new CPSChart("cps-chart");

  // 初始化信息显示（不传入静态数据，让组件自动从API获取）
  const infoDisplay = new InfoDisplay("info-container");

  // 导出组件实例，方便后续可能的交互
  window.powerCharts = {
    // balanceChart,
    // windChart,
    // solarChart,
    sectionTable,
    cpsChart,
    infoDisplay,
  };

  console.log("全网平衡监视系统初始化完成");

  // 初始化时确保布局比例
  // ensureLayoutProportions();

  // 添加窗口大小变化的监听器
  // window.addEventListener("resize", ensureLayoutProportions);
});

// 模拟数据更新示例（实际项目中可能通过API获取实时数据）

// 启动模拟数据更新
// simulateDataUpdate();
