import { defineConfig } from 'vite'

export default defineConfig({
  // 开发服务器配置
  server: {
    port: 3000,
    open: true,
    // 代理配置
    proxy: {
      '/dwyztApp/dwyzt': {
        target: 'http://172.19.139.41:8087/mock/189/dwyzt',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/dwyztApp\/dwyzt/, '')
      }
    }
  },
  
  // 构建配置
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    rollupOptions: {
      input: {
        main: './index.html'
      }
    }
  },
  
  // 基础路径配置
  base: './',
  
  // 静态资源处理
  assetsInclude: ['**/*.webp']
})
